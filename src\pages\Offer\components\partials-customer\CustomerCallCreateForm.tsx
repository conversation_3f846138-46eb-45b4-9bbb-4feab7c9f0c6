import type { Dispatch, SetStateAction } from "react";
import React, { useEffect, useRef } from "react";
import type { ProFormInstance } from "@ant-design/pro-form";
import { ModalForm } from "@ant-design/pro-form";
import { addCustomerCall } from "@/services/app/Customer/customer-call";
import { CheckboxOptionType, message } from "antd";
import Util, { getFormData } from "@/util";
import { SupplierCallType, SupplierCallTypeKv } from "@/constants";
import { ProForm, ProFormRadio } from "@ant-design/pro-components";
import { useModel } from "@umijs/max";
import HtmlEditor from "@/components/HtmlEditor";
import { UploadFile } from "antd/es/upload";

type FormValueType = Omit<Partial<API.CustomerCall>, "files"> & {
  copy_to_sk?: "" | "yes" | "yes2";
} & { files?: UploadFile[] };

const handleAdd = async (fields: FormValueType) => {
  const hide = message.loading("Adding...", 0);

  const formData = getFormData(fields);

  if (fields.files?.length) {
    fields.files.forEach((file, ind) => {
      formData.set(`files[${ind}]`, file?.originFileObj ?? "");
    });
  }

  try {
    await addCustomerCall(formData);
    message.success("Added successfully");
    return true;
  } catch (error: any) {
    Util.error(error);
    return false;
  } finally {
    hide();
  }
};

export type CustomerCallCreateFormProps = {
  initialValues?: Partial<API.CustomerCall>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: FormValueType) => void;
};

const CustomerCallTypeOptions: CheckboxOptionType[] = [
  {
    value: SupplierCallType.Phone,
    label: SupplierCallTypeKv[SupplierCallType.Phone],
  },
  {
    value: SupplierCallType.Email,
    label: SupplierCallTypeKv[SupplierCallType.Email],
  },
  {
    value: SupplierCallType.Meeting,
    label: SupplierCallTypeKv[SupplierCallType.Meeting],
  },
  {
    value: SupplierCallType.Notes,
    label: SupplierCallTypeKv[SupplierCallType.Notes],
  },
];

const CustomerCallCreateForm: React.FC<CustomerCallCreateFormProps> = (props) => {
  const { initialValues, modalVisible, handleModalVisible, onSubmit } = props;

  const { initialState } = useModel("@@initialState");

  const formRef = useRef<ProFormInstance>();

  useEffect(() => {
    if (modalVisible) {
      // const savedFormValues = Util.getSfValues('f_new_call', {});
      formRef.current?.resetFields();
      formRef.current?.setFieldsValue({
        ...(initialValues ?? {
          type: SupplierCallType.Phone,
          // direction: SupplierCallDirection.Out,
        }),
      });
    }
  }, [initialState?.currentUser?.initials, initialValues, modalVisible]);

  return (
    <ModalForm<FormValueType>
      title={"New Customer Conversion"}
      width="800px"
      open={modalVisible}
      onOpenChange={handleModalVisible}
      layout="horizontal"
      labelAlign="left"
      labelCol={{ flex: "90px" }}
      wrapperCol={{ flex: "auto" }}
      formRef={formRef}
      onFinish={async (value) => {
        Util.setSfValues("f_new_call", { ...value, note: null });

        const newValue: any = {
          ...value,
          customer_id: initialValues?.customer_id,
          offer_no: initialValues?.offer_no,
        } as API.CustomerCall;

        const success = await handleAdd(newValue);
        if (success) {
          if (formRef.current) formRef.current.resetFields();
          if (onSubmit) onSubmit(value);
          handleModalVisible(false);
        }
      }}
    >
      <ProFormRadio.Group name="type" label="Type" options={CustomerCallTypeOptions} />

      <ProForm.Item name={"note"} label={"Notes"} style={{ width: "100%" }} labelCol={undefined} wrapperCol={{ span: 24 }}>
        <HtmlEditor id={`call_note_create_customer`} initialFocus enableTextModule hideMenuBar toolbarMode={2} height={400} />
      </ProForm.Item>
    </ModalForm>
  );
};

export default CustomerCallCreateForm;
