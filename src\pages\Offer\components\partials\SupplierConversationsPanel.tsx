import { But<PERSON>, Col, message, Row } from "antd";
import { useMemo, useRef, useState } from "react";
import SupplierCallCreateForm from "./SupplierCallCreateForm";
import { SupplierCallDirection, SupplierCallType } from "@/constants";
import { ActionType } from "@ant-design/pro-components";
import SupplierCallListByOffer from "./SupplierCallListByOffer";

type SupplierConversationsPanelProps = {
  supplier?: API.Supplier;
  offer_no?: string | number;
};

const SupplierConversationsPanel: React.FC<SupplierConversationsPanelProps> = ({ supplier, offer_no }) => {
  return (
    <div>
      <h3>Supplier: {supplier?.name}</h3>
      <SupplierCallListByOffer offer_no={offer_no} supplier={supplier} />
    </div>
  );
};

export default SupplierConversationsPanel;
