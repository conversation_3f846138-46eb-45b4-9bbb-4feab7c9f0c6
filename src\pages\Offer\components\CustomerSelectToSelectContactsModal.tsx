import Util, { sn } from "@/util";
import { ActionType, ProColumns, ProForm, ProFormInstance, ProFormText, ProTable } from "@ant-design/pro-components";
import { <PERSON>ton, Card, Col, Modal, Row, Space } from "antd";
import { Dispatch, SetStateAction, useEffect, useRef, useState } from "react";
import { getCustomerListByPage } from "@/services/app/Customer/customer";
import CustomerContactSelectModalV2 from "../../Customer/Customer/components/CustomerContactSelectModalV2";

export type CustomerSelectToSelectContactsModalSearchParamsType = {
  byOfferNo?: number | string;
  offerNo?: number | string;
};

export type SearchFormValueType = Partial<API.Customer> & API.PageParams;

type RowType = API.Customer;

type CustomerSelectToSelectContactsModalProps = {
  searchParams?: CustomerSelectToSelectContactsModalSearchParamsType;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
};

const CustomerSelectToSelectContactsModal: React.FC<CustomerSelectToSelectContactsModalProps> = (props) => {
  const { searchParams, modalVisible, handleModalVisible } = props;

  const searchFormRef = useRef<ProFormInstance>();
  const actionRef = useRef<ActionType>();

  const [loading, setLoading] = useState<boolean>(false);
  const [currentRow, setCurrentRow] = useState<RowType>();
  const [selectedRows, setSelectedRows] = useState<RowType[]>([]);

  // bulk customer selection and emailing
  const [openCustomerContactSelectionModal, setOpenCustomerContactSelectionModal] = useState<boolean>(false);

  const columns: ProColumns<RowType>[] = [
    {
      title: "Customer",
      dataIndex: ["name"],
      width: 170,
      showSorterTooltip: false,
      copyable: true,
    },
    {
      title: "OrgA",
      dataIndex: "org_a",
      width: 50,
      align: "center",
    },
    {
      title: "Rel.",
      dataIndex: "relevance",
      width: 50,
      showSorterTooltip: false,
      align: "center",
      className: "p-0",
      render(__, entity) {
        const relevance = entity.ext?.relevance;
        return `${relevance ?? ""}` ? Util.numTo2Digits(relevance) : "";
      },
    },
  ];

  useEffect(() => {
    if (modalVisible && searchParams?.byOfferNo) {
      actionRef.current?.reload();
    }
  }, [modalVisible, searchParams?.byOfferNo]);

  return (
    <Modal
      title={<>Select customers to send emails</>}
      open={modalVisible}
      onCancel={() => handleModalVisible(false)}
      width="950px"
      footer={false}
      styles={{ body: { paddingTop: 0 } }}
    >
      <Card variant="borderless">
        <ProForm<SearchFormValueType>
          layout="inline"
          formRef={searchFormRef}
          isKeyPressSubmit
          className="search-form"
          submitter={{
            searchConfig: { submitText: "Search" },
            submitButtonProps: { htmlType: "submit" },
            onSubmit: () => actionRef.current?.reload(),
            onReset: () => {
              searchFormRef.current?.resetFields();
              actionRef.current?.reload();
            },
          }}
        >
          <ProFormText name={"keyWords"} label="KeyWords" width={140} placeholder={"Email / Name / Contact Names"} />
        </ProForm>
      </Card>

      <ProTable<RowType, API.PageParams>
        headerTitle={false}
        toolBarRender={() => []}
        actionRef={actionRef}
        rowKey="id"
        size="small"
        revalidateOnFocus={false}
        options={false}
        search={false}
        sticky
        scroll={{ x: 800 }}
        pagination={{
          defaultPageSize: 20,
        }}
        request={async (params, sort, filter) => {
          const searchFormValues = searchFormRef.current?.getFieldsValue();
          Util.setSfValues("cu_sf_customer_m", searchFormValues);
          Util.setSfValues("cu_sf_customer_m_p", params);

          setLoading(true);
          return getCustomerListByPage(
            {
              ...searchFormValues,
              ...params,
              byOfferNo: searchParams?.byOfferNo,
              with: "existsOfferNoStatus",
            },
            sort,
            filter,
          )
            .then((res) => {
              if (currentRow?.id) {
                setCurrentRow(res.data.find((x) => x.id == currentRow.id));
              }
              return res;
            })
            .finally(() => setLoading(false));
        }}
        rowSelection={{
          selectedRowKeys: selectedRows?.map((x) => x.id as React.Key),
          onChange: (_, selectedRows) => {
            setSelectedRows(selectedRows);
          },
        }}
        onRequestError={Util.error}
        columns={columns}
        columnEmptyText=""
        tableAlertOptionRender={(alertProps) => {
          return (
            <Space size={8}>
              <Button
                type="primary"
                onClick={() => {
                  setOpenCustomerContactSelectionModal(true);
                }}
              >
                Select contacts to send emails
              </Button>
              <Button type="link" onClick={alertProps.onCleanSelected}>
                Clear
              </Button>
            </Space>
          );
        }}
      />

      <CustomerContactSelectModalV2
        searchParams={{ offerNo: searchParams?.offerNo, customerIds: selectedRows.map((x) => x.id) }}
        modalVisible={openCustomerContactSelectionModal}
        handleModalVisible={setOpenCustomerContactSelectionModal}
      />
    </Modal>
  );
};

export default CustomerSelectToSelectContactsModal;
