import { request } from 'umi';
import { paramsSerializer } from '../api';
import { RequestConfig } from '@umijs/max';
import { RequestOptionsType } from '@ant-design/pro-components';

const urlPrefix = '/api/offer/blog';

/**
 * Get OfferBlogs list
 *
 * GET /api/offer/blog
 */
export async function getOfferBlogListByPage(params: API.PageParams, sort?: any, filter?: any) {
  return request<API.ResultList<API.OfferBlog>>(`${urlPrefix}`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort,
      filter,
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
    pagination: res.message.pagination, // For total row pagination hack.
  }));
}

export async function getOfferBlog(id?: number, params?: API.OfferBlog & API.PageParams) {
  return getOfferBlogListByPage({ id, ...params, pageSize: 1, page: 1 }).then((res) => {
    return res.data?.[0];
  });
}


export async function getOfferBlogByOfferNo(offerNo?: number | string, params?: API.OfferBlog & API.PageParams) {
  return getOfferBlogListByPage({ offer_no: offerNo, ...params, pageSize: 1, page: 1 }).then((res) => {
    return res.data?.[0];
  });
}

/**
 * Create Offer Blog.
 *
 *  POST /api/offer/blog */
export async function addOfferBlog(
  data?: API.OfferBlog | FormData,
  options?: { [key: string]: any },
) {
  const config: RequestConfig = {
    method: 'POST',
    data,
    ...(options || {}),
  };
  return request<API.ResultObject<API.OfferBlog>>(`${urlPrefix}`, config).then((res) => res.message);
}

/**
 * Create Offer Blog.
 *
 *  POST /api/offer/blog/updateOrCreate */
export async function updateOrCreateOfferBlogByOfferNo(
  data?: API.OfferBlog | FormData,
  options?: { [key: string]: any },
) {
  const config: RequestConfig = {
    method: 'POST',
    data,
    ...(options || {}),
  };
  return request<API.ResultObject<API.OfferBlog>>(`${urlPrefix}/updateOrCreate`, config).then((res) => res.message);
}

/**
 * Update OfferBlog data.
 *
 *  PUT /api/offer/blog/{id}/update */
export async function updateOfferBlog(
  id?: number,
  data?: API.OfferBlog | FormData,
  options?: { [key: string]: any },
) {
  const config: RequestConfig = {
    method: 'PUT',
    data,
    ...(options || {}),
  };
  return request<API.ResultObject<API.OfferBlog>>(`${urlPrefix}/${id}/update`, config).then(
    (res) => res.message,
  );
}


/**
 * Get AC List dropdown list.
 *
 * GET /api/offer/blog/getOfferBlogACList
 */
export async function getOfferBlogACList(params: API.PageParams) {
  return request<API.ResultObject<RequestOptionsType[]>>(`${urlPrefix}/getOfferBlogACList`, {
    method: 'GET',
    params: {
      ...params,
      perPage: 500,
      page: params.current,
      sort: { id: 'descend' },
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => res.message);
}

/** delete DELETE /api/offer/blog/{id} */
export async function deleteOfferBlog(id?: string | number, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${urlPrefix}/${id}`, {
    method: 'DELETE',
    ...(options || {}),
  });
}
