import { <PERSON><PERSON>, Col, message, Row, Space, Spin } from "antd";
import SupplierConversationsPanel from "./partials/SupplierConversationsPanel";
import { ProForm, ProFormInstance, ProFormTextArea } from "@ant-design/pro-components";
import CustomerConversationsPanel from "./partials-customer/CustomerConversationsPanel";
import { Dispatch, SetStateAction, useEffect, useRef, useState } from "react";
import { getOfferExtByOfferNo, updateOrCreateOfferExt } from "@/services/app/Offer/offer-ext";
import Util from "@/util";

type ConversationsMixedPanelSearchParamsType = { tmp?: any };

type ConversationsMixedPanelProps = {
  searchParams?: ConversationsMixedPanelSearchParamsType;
  isEditing?: number;
  loadOrgOfferDetail?: () => void;
  orgOffer?: APIOrg.Offer;

  loadOfferExt?: () => void;
  offerExt?: API.OfferExt;
  loadingExt?: boolean;
  setLoadingExt?: Dispatch<SetStateAction<boolean>>;
};

const ConversationsMixedPanel: React.FC<ConversationsMixedPanelProps> = ({
  orgOffer,
  isEditing,
  loadOrgOfferDetail,
  loadOfferExt,
  offerExt,
  loadingExt,
  setLoadingExt,
}) => {
  const formRef = useRef<ProFormInstance>();

  useEffect(() => {
    if (offerExt) {
      formRef.current?.setFieldsValue(offerExt);
    } else {
      formRef.current?.resetFields();
    }
  }, [offerExt]);

  return (
    <Row gutter={16}>
      <Col span={9}>
        <SupplierConversationsPanel supplier={orgOffer?.supp_supplier} offer_no={orgOffer?.offer_sid} />
      </Col>
      <Col span={6}>
        <Spin spinning={loadingExt}>
          <ProForm
            formRef={formRef}
            submitter={{
              render: () => {
                return (
                  <Space size={12}>
                    <Button
                      type="primary"
                      onClick={() => {
                        const values = formRef.current?.getFieldsValue();
                        const hide = message.loading("Saving offer notes...", 0);
                        updateOrCreateOfferExt({ ...values, offer_no: orgOffer?.offer_sid })
                          .then((res) => {
                            hide();
                            message.success("Offer notes saved successfully.");
                            loadOfferExt?.();
                          })
                          .catch(Util.error)
                          .finally(() => {
                            hide();
                          });
                      }}
                    >
                      Save
                    </Button>
                  </Space>
                );
              },
            }}
          >
            <h3>Internal (EK/VK):</h3>
            <ProFormTextArea name="notes_ek_vk" fieldProps={{ rows: 2 }} placeholder="Enter Internal (EK/VK)" />

            <h3>Notes:</h3>
            <ProFormTextArea name="notes" fieldProps={{ rows: 15 }} placeholder={"Enter offer notes."} />
          </ProForm>
        </Spin>
      </Col>
      <Col span={9}>
        <CustomerConversationsPanel offer_no={orgOffer?.offer_sid} />
      </Col>
    </Row>
  );
};

export default ConversationsMixedPanel;
